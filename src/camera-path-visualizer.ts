import { Entity, Vec3, StandardMaterial, Color } from 'playcanvas';
import * as pc from 'playcanvas';

import { Events } from './events';
import { Scene } from './scene';
import { Pose } from './camera-poses';

export class CameraPathVisualizer {
    events: Events;
    scene: Scene;
    visible = false;
    
    // 3D entities for visualization
    pathLineEntity: Entity = null;
    keyframeMarkers: Entity[] = [];
    
    // Path data
    poses: Pose[] = [];
    
    constructor(events: Events, scene: Scene) {
        this.events = events;
        this.scene = scene;
        
        this.setupEventListeners();
    }
    
    private setupEventListeners() {
        // Listen for pose changes
        this.events.on('camera.poses.changed', () => {
            this.updateVisualization();
        });
        
        // Listen for visibility toggle
        this.events.on('camera.path.setVisible', (visible: boolean) => {
            this.setVisible(visible);
        });
        
        // Listen for pose updates
        this.events.on('camera.addPose', () => {
            this.updateVisualization();
        });
        
        this.events.on('camera.updatePose', () => {
            this.updateVisualization();
        });
        
        this.events.on('timeline.remove', () => {
            this.updateVisualization();
        });
    }
    
    setVisible(visible: boolean) {
        this.visible = visible;
        
        if (visible) {
            this.updateVisualization();
        } else {
            this.clearVisualization();
        }
    }
    
    private updateVisualization() {
        if (!this.visible) return;
        
        // Get current poses
        this.poses = this.events.invoke('camera.poses') || [];
        
        if (this.poses.length < 2) {
            this.clearVisualization();
            return;
        }
        
        // Clear existing visualization
        this.clearVisualization();
        
        // Create keyframe markers
        this.createKeyframeMarkers();
        
        // Create path line
        this.createPathLine();
    }
    
    private createKeyframeMarkers() {
        this.poses.forEach((pose, index) => {
            const marker = this.createKeyframeMarker(pose, index);
            this.keyframeMarkers.push(marker);
        });
    }
    
    private createKeyframeMarker(pose: Pose, index: number): Entity {
        const marker = new Entity(`cameraKeyframe_${index}`);
        
        // Create material
        const material = new pc.StandardMaterial();
        material.diffuse.set(0.2, 0.8, 1.0); // Light blue
        material.emissive.set(0.1, 0.4, 0.5);
        material.update();
        
        // Add render component
        marker.addComponent('render', {
            type: 'sphere',
            material: material
        });
        
        // Set position
        marker.setPosition(pose.position);
        
        // Set scale based on scene size
        const scale = this.calculateMarkerScale();
        marker.setLocalScale(scale, scale, scale);
        
        // Add click interaction
        this.addMarkerClickHandler(marker, pose.frame);
        
        // Add to scene
        this.scene.app.root.addChild(marker);
        
        return marker;
    }
    
    private createPathLine() {
        if (this.poses.length < 2) return;
        
        // Create line segments between consecutive keyframes
        for (let i = 0; i < this.poses.length - 1; i++) {
            const startPose = this.poses[i];
            const endPose = this.poses[i + 1];
            
            const lineEntity = this.createLineSegment(startPose.position, endPose.position);
            this.scene.app.root.addChild(lineEntity);
        }
    }
    
    private createLineSegment(start: Vec3, end: Vec3): Entity {
        const lineEntity = new Entity('cameraPathSegment');
        lineEntity.tags.add('cameraPathSegment')
        
        // Create material
        const material = new pc.StandardMaterial();
        material.diffuse.set(1, 0.8, 0.2); // Orange
        material.emissive.set(0.5, 0.4, 0.1);
        material.update();
        
        // Add render component as cylinder
        lineEntity.addComponent('render', {
            type: 'cylinder',
            material: material
        });
        
        // Calculate position, rotation and scale
        const midPoint = new Vec3().add2(start, end).mulScalar(0.5);
        const distance = start.distance(end);
        const direction = new Vec3().sub2(end, start).normalize();
        
        lineEntity.setPosition(midPoint);
        
        // Orient the cylinder along the line
        const up = new Vec3(0, 1, 0);
        const dot = direction.dot(up);
        
        if (Math.abs(dot) > 0.99) {
            // Handle case where direction is parallel to up vector
            const right = new Vec3(1, 0, 0);
            lineEntity.lookAt(direction.x > 0 ? right : new Vec3(-1, 0, 0));
        } else {
            lineEntity.lookAt(end);
        }
        
        lineEntity.rotateLocal(90, 0, 0);
        
        // Set scale - thin cylinder
        const thickness = this.calculateLineThickness();
        lineEntity.setLocalScale(thickness, distance, thickness);
        
        return lineEntity;
    }
    
    private addMarkerClickHandler(marker: Entity, frame: number) {
        // Store frame data on the entity for click detection
        (marker as any).cameraFrame = frame;
        
        // Note: Actual click detection would need to be implemented in the main scene
        // mouse handling system. For now, we'll fire an event that can be caught
        // by the scene's click handler.
    }
    
    private calculateMarkerScale(): number {
        // Calculate appropriate scale based on scene bounds
        const sceneBound = this.scene.bound;
        if (!sceneBound) return 0.1;
        
        const sceneSize = sceneBound.halfExtents.length();
        return Math.max(0.05, sceneSize * 0.02);
    }
    
    private calculateLineThickness(): number {
        // Calculate appropriate line thickness based on scene bounds
        const sceneBound = this.scene.bound;
        if (!sceneBound) return 0.01;
        
        const sceneSize = sceneBound.halfExtents.length();
        return Math.max(0.005, sceneSize * 0.005);
    }
    
    private clearVisualization() {
        // Remove keyframe markers
        this.keyframeMarkers.forEach(marker => {
            if (marker.parent) {
                marker.parent.removeChild(marker);
            }
            marker.destroy();
        });
        this.keyframeMarkers = [];
        
        // Remove path line entities
        const pathEntities = this.scene.app.root.findByTag('cameraPathSegment');
        pathEntities.forEach(entity => {
            if (entity.parent) {
                entity.parent.removeChild(entity);
            }
            entity.destroy();
        });
        
        // Force scene re-render
        this.scene.forceRender = true;
    }
    
    // Method to handle marker clicks (to be called from scene mouse handler)
    handleMarkerClick(entity: Entity): boolean {
        const frame = (entity as any).cameraFrame;
        if (frame !== undefined) {
            // Move timeline cursor to this frame
            this.events.fire('timeline.setFrame', frame);
            return true;
        }
        return false;
    }
    
    destroy() {
        this.clearVisualization();
    }
}
