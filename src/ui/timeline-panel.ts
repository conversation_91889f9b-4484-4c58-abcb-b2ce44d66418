import { Button, Container, NumericInput, SelectInput } from '@playcanvas/pcui';
import cameraPanelSvg from './svg/camera-panel.svg';

import { Events } from '../events';
import { Tooltips } from './tooltips';
import { CameraPosePanel } from './camera-pose-panel';
import { CameraPathVisualizer } from '../camera-path-visualizer';

const createSvg = (svgString: string) => {
    const decodedStr = decodeURIComponent(svgString.substring('data:image/svg+xml,'.length));
    return new DOMParser().parseFromString(decodedStr, 'image/svg+xml').documentElement;
};

class Ticks extends Container {
    private keys: HTMLElement[] = [];
    private offsetFromFrame: (frame: number) => number = () => 0;

    constructor(events: Events, tooltips: Tooltips, args = {}) {
        args = {
            ...args,
            id: 'ticks'
        };

        super(args);

        const workArea = new Container({
            id: 'ticks-area'
        });

        this.append(workArea);

        let addKey: (value: number) => void;
        let removeKey: (index: number) => void;
        let frameFromOffset: (offset: number) => number;
        let moveCursor: (frame: number) => void;

        // rebuild the timeline
        const rebuild = () => {
            // clear existing labels
            workArea.dom.innerHTML = '';

            const numFrames = events.invoke('timeline.frames');
            const currentFrame = events.invoke('timeline.frame');

            const padding = 20;
            const width = this.dom.getBoundingClientRect().width - padding * 2;
            const labelStep = Math.max(1, Math.floor(numFrames / Math.max(1, Math.floor(width / 50))));
            const numLabels = Math.max(1, Math.ceil(numFrames / labelStep));

            this.offsetFromFrame = (frame: number) => {
                return padding + Math.floor(frame / (numFrames - 1) * width);
            };

            frameFromOffset = (offset: number) => {
                return Math.max(0, Math.min(numFrames - 1, Math.floor((offset - padding) / width * (numFrames - 1))));
            };

            // timeline labels

            for (let i = 0; i < numLabels; i++) {
                const thisFrame = Math.floor(i * labelStep);
                const label = document.createElement('div');
                label.classList.add('time-label');
                label.style.left = `${this.offsetFromFrame(thisFrame)}px`;
                label.textContent = thisFrame.toString();
                workArea.dom.appendChild(label);
            }

            // keys

            this.keys = [];
            const createKey = (value: number, index: number) => {
                const label = document.createElement('div');
                label.classList.add('time-label', 'key');
                label.style.left = `${this.offsetFromFrame(value)}px`;
                label.dataset.keyIndex = index.toString();
                label.dataset.keyFrame = value.toString();
                workArea.dom.appendChild(label);
                this.keys.push(label);
            };

            (events.invoke('timeline.keys') as number[]).forEach((value, index) => createKey(value, index));

            addKey = (value: number) => {
                const index = this.keys.length;
                createKey(value, index);
            };

            removeKey = (index: number) => {
                workArea.dom.removeChild(this.keys[index]);
                this.keys.splice(index, 1);
                // Update indices for remaining keys
                this.keys.forEach((key, i) => {
                    key.dataset.keyIndex = i.toString();
                });
            };

            // cursor

            const cursor = document.createElement('div');
            cursor.classList.add('time-label', 'cursor');
            cursor.style.left = `${this.offsetFromFrame(currentFrame)}px`;
            cursor.textContent = currentFrame.toString();
            workArea.dom.appendChild(cursor);

            moveCursor = (frame: number) => {
                cursor.style.left = `${this.offsetFromFrame(frame)}px`;
                cursor.textContent = frame.toString();
            };
        };

        // handle scrubbing and key dragging

        let scrubbing = false;
        let draggingKey = false;
        let draggedKeyIndex = -1;
        let draggedKeyElement: HTMLElement | null = null;
        let keyClickStartTime = 0;
        let keyClickStartPos = { x: 0, y: 0 };
        let hasMoved = false;

        workArea.dom.addEventListener('pointerdown', (event: PointerEvent) => {
            if (!scrubbing && !draggingKey && event.isPrimary) {
                const target = event.target as HTMLElement;

                // Check if clicking on a key
                if (target && target.classList.contains('key')) {
                    keyClickStartTime = Date.now();
                    keyClickStartPos = { x: event.clientX, y: event.clientY };
                    hasMoved = false;

                    draggingKey = true;
                    draggedKeyIndex = parseInt(target.dataset.keyIndex || '-1');
                    draggedKeyElement = target;
                    target.classList.add('dragging');
                    workArea.dom.setPointerCapture(event.pointerId);
                    event.stopPropagation();
                } else {
                    // Regular timeline scrubbing
                    scrubbing = true;
                    workArea.dom.setPointerCapture(event.pointerId);
                    events.fire('timeline.setFrame', frameFromOffset(event.offsetX));
                }
            }
        });

        workArea.dom.addEventListener('pointermove', (event: PointerEvent) => {
            if (draggingKey && draggedKeyElement) {
                // Check if mouse has moved significantly (more than 3 pixels)
                const deltaX = Math.abs(event.clientX - keyClickStartPos.x);
                const deltaY = Math.abs(event.clientY - keyClickStartPos.y);
                if (deltaX > 3 || deltaY > 3) {
                    hasMoved = true;
                }

                // Update key position during drag
                const newFrame = Math.max(0, Math.min(frameFromOffset(event.offsetX), events.invoke('timeline.frames') - 1));
                draggedKeyElement.style.left = `${this.offsetFromFrame(newFrame)}px`;
                draggedKeyElement.dataset.keyFrame = newFrame.toString();
            } else if (scrubbing) {
                events.fire('timeline.setFrame', frameFromOffset(event.offsetX));
            }
        });

        workArea.dom.addEventListener('pointerup', (event: PointerEvent) => {
            if (draggingKey && event.isPrimary && draggedKeyElement) {
                const clickDuration = Date.now() - keyClickStartTime;

                // Check if this was a click (short duration and minimal movement)
                if (!hasMoved && clickDuration < 300) {
                    // Single click on key - move cursor to this key's frame
                    const keyFrame = parseInt(draggedKeyElement.dataset.keyFrame || '0');
                    events.fire('timeline.setFrame', keyFrame);

                    // Reset the key position (in case it was moved slightly during click)
                    const originalFrame = (events.invoke('timeline.keys') as number[])[draggedKeyIndex];
                    draggedKeyElement.style.left = `${this.offsetFromFrame(originalFrame)}px`;
                    draggedKeyElement.dataset.keyFrame = originalFrame.toString();
                } else {
                    // This was a drag - finalize key position
                    const newFrame = Math.max(0, Math.min(frameFromOffset(event.offsetX), events.invoke('timeline.frames') - 1));
                    const roundedFrame = Math.round(newFrame);

                    // Update the key position in the timeline data
                    events.fire('timeline.setKey', draggedKeyIndex, roundedFrame);

                    // Update UI
                    draggedKeyElement.style.left = `${this.offsetFromFrame(roundedFrame)}px`;
                    draggedKeyElement.dataset.keyFrame = roundedFrame.toString();
                }

                // Clean up drag state
                draggedKeyElement.classList.remove('dragging');
                workArea.dom.releasePointerCapture(event.pointerId);
                draggingKey = false;
                draggedKeyIndex = -1;
                draggedKeyElement = null;
            } else if (scrubbing && event.isPrimary) {
                workArea.dom.releasePointerCapture(event.pointerId);
                scrubbing = false;
            }
        });

        // rebuild the timeline on dom resize
        new ResizeObserver(() => rebuild()).observe(workArea.dom);

        // rebuild when timeline frames change
        events.on('timeline.frames', () => {
            rebuild();
        });

        events.on('timeline.frame', (frame: number) => {
            moveCursor(frame);
        });

        events.on('timeline.keyAdded', (value: number) => {
            addKey(value);
        });

        events.on('timeline.keyRemoved', (index: number) => {
            removeKey(index);
        });

        events.on('timeline.keySet', (index: number, frame: number) => {
            if (this.keys[index]) {
                this.keys[index].style.left = `${this.offsetFromFrame(frame)}px`;
                this.keys[index].dataset.keyFrame = frame.toString();
            }
        });
    }
}

class TimelinePanel extends Container {
    private spacerL: Container;
    private cameraPosePanel: CameraPosePanel;
    private pathVisualizer: CameraPathVisualizer;
    private pathVisible = false;

    constructor(events: Events, tooltips: Tooltips, args = {}) {
        args = {
            ...args,
            id: 'timeline-panel'
        };

        super(args);

        // play controls

        const prev = new Button({
            class: 'button',
            text: '\uE162'
        });

        const play = new Button({
            class: 'button',
            text: '\uE131'
        });

        const next = new Button({
            class: 'button',
            text: '\uE164'
        });

        // key controls

        const addKey = new Button({
            class: 'button',
            text: '\uE120'
        });

        const removeKey = new Button({
            class: 'button',
            text: '\uE121',
            enabled: false
        });

        const poseButton = new Button({
            //class: 'button',
            class: 'custom-icon-button'
        });
        poseButton.dom.appendChild(createSvg(cameraPanelSvg));

        const buttonControls = new Container({
            id: 'button-controls'
        });
        buttonControls.append(prev);
        buttonControls.append(play);
        buttonControls.append(next);
        buttonControls.append(addKey);
        buttonControls.append(removeKey);
        buttonControls.append(poseButton);

        // settings

        const speed = new SelectInput({
            id: 'speed',
            defaultValue: 30,
            options: [
                { v: 1, t: '1 fps' },
                { v: 6, t: '6 fps' },
                { v: 12, t: '12 fps' },
                { v: 24, t: '24 fps' },
                { v: 30, t: '30 fps' },
                { v: 60, t: '60 fps' }
            ]
        });

        speed.on('change', (value: string) => {
            events.fire('timeline.setFrameRate', parseInt(value, 10));
        });

        events.on('timeline.frameRate', (frameRate: number) => {
            speed.value = frameRate.toString();
        });

        const frames = new NumericInput({
            id: 'totalFrames',
            value: 180,
            min: 1,
            max: 10000,
            precision: 0
        });

        frames.on('change', (value: number) => {
            events.fire('timeline.setFrames', value);
        });

        events.on('timeline.frames', (framesIn: number) => {
            frames.value = framesIn;
        });

        const settingsControls = new Container({
            id: 'settings-controls'
        });
        settingsControls.append(speed);
        settingsControls.append(frames);

        // append control groups

        const controlsWrap = new Container({
            id: 'controls-wrap'
        });

        this.spacerL = new Container({
            class: ['spacer', 'left']
        });

        const spacerR = new Container({
            class: 'spacer'
        });
        spacerR.append(settingsControls);

        controlsWrap.append(this.spacerL);
        controlsWrap.append(buttonControls);
        controlsWrap.append(spacerR);

        const ticks = new Ticks(events, tooltips);

        this.append(controlsWrap);
        this.append(ticks);

        // ui handlers

        const skip = (dir: 'forward' | 'back') => {
            const orderedKeys = (events.invoke('timeline.keys') as number[]).map((frame, index) => {
                return { frame, index };
            }).sort((a, b) => a.frame - b.frame);

            if (orderedKeys.length > 0) {
                const frame = events.invoke('timeline.frame');
                const nextKey = orderedKeys.findIndex(k => (dir === 'back' ? k.frame >= frame : k.frame > frame));
                const l = orderedKeys.length;

                if (nextKey === -1) {
                    events.fire('timeline.setFrame', orderedKeys[dir === 'back' ? l - 1 : 0].frame);
                } else {
                    events.fire('timeline.setFrame', orderedKeys[dir === 'back' ? (nextKey + l - 1) % l : nextKey].frame);
                }
            } else {
                // if there are no keys, just to start of timeline or end
                if (dir === 'back') {
                    events.fire('timeline.setFrame', 0);
                } else {
                    events.fire('timeline.setFrame', events.invoke('timeline.frames') - 1);
                }
            }
        };

        prev.on('click', () => {
            skip('back');
        });

        play.on('click', () => {
            if (events.invoke('timeline.playing')) {
                events.fire('timeline.setPlaying', false);
                play.text = '\uE131';
            } else {
                events.fire('timeline.setPlaying', true);
                play.text = '\uE135';
            }
        });

        next.on('click', () => {
            skip('forward');
        });

        addKey.on('click', () => {
            events.fire('timeline.add', events.invoke('timeline.frame'));
        });

        removeKey.on('click', () => {
            const index = events.invoke('timeline.keys').indexOf(events.invoke('timeline.frame'));
            if (index !== -1) {
                events.fire('timeline.remove', index);
            }
        });

        poseButton.on('click', () => {
            if (!this.cameraPosePanel) {
                this.cameraPosePanel = new CameraPosePanel(events, tooltips);
                this.spacerL.append(this.cameraPosePanel);
                this.cameraPosePanel.hidden = false;

                // Initialize path visualizer if not already done
                if (!this.pathVisualizer) {
                    // Get scene reference from events
                    const scene = events.invoke('scene');
                    if (scene) {
                        this.pathVisualizer = new CameraPathVisualizer(events, scene);
                        // Register the visualizer with the scene for click handling
                        events.fire('scene.setPathVisualizer', this.pathVisualizer);
                    }
                }

                // Toggle path visibility
                this.pathVisible = !this.pathVisible;
                if (this.pathVisualizer) {
                    this.pathVisualizer.setVisible(this.pathVisible);
                }
                return;
            }

            this.cameraPosePanel.hidden = !this.cameraPosePanel.hidden;

            // Toggle path visibility when panel is shown/hidden
            if (!this.cameraPosePanel.hidden) {
                this.pathVisible = true;
                if (this.pathVisualizer) {
                    this.pathVisualizer.setVisible(true);
                }
            } else {
                this.pathVisible = false;
                if (this.pathVisualizer) {
                    this.pathVisualizer.setVisible(false);
                }
            }
        });

        const canDelete = (frame: number) => events.invoke('timeline.keys').includes(frame);

        events.on('timeline.frame', (frame: number) => {
            removeKey.enabled = canDelete(frame);
        });

        events.on('timeline.keyRemoved', (index: number) => {
            removeKey.enabled = canDelete(events.invoke('timeline.frame'));
        });

        events.on('timeline.keyAdded', (frame: number) => {
            removeKey.enabled = canDelete(frame);
        });

        // cancel animation playback if user interacts with camera
        events.on('camera.controller', (type: string) => {
            if (events.invoke('timeline.playing')) {
                // stop
            }
        });
    }
}

export { TimelinePanel };
